'use client'

import { useC<PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  encodeBase64,
  getCategoriesQuery,
  type HomeProduct,
  isBase64,
  type RedirectType,
  resolveCatchMessage,
  TCatchMessage,
  TRACK_EVENT,
  updateProductList,
  useAuth,
  useGetCategoryProductsQuery,
  usePagination,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'

import {
  Breadcrumb,
  BreadcrumbSkeleton,
  ProductCardSkeleton,
  ProductList,
  ProductListSkeleton,
  Skeleton,
} from '@/components'
import { useBreadcrumbs } from '@/hooks/useBreadcrumbs'
import { usePathname } from '@/i18n/navigation'

import { TCategoryItem } from '../../CategoryPage'
import CategoryNav from '../categoryNav/CategoryNav'

export type TCategoryNav = {
  id: string
  name: string
  url: string
  children?: TCategoryNav[]
  parent?: TCategoryNav
}

type BannerData = {
  title?: string | null
  subtitle?: string | null
  image?: string | null
  description?: string | null
  buttonText?: string | null
  url?: {
    type?: RedirectType | null
    url?: string | null
    value?: string | null
  } | null
  widthRatio: number | null
  heightRatio: number | null
} | null

type LoadingState = {
  isLoading: boolean
  isFetching: boolean
}

const CategoryList = ({ uid, categoryData }: { uid: string; categoryData?: TCategoryItem }) => {
  // Hooks（钩子函数）
  const pathname = usePathname()
  const getI18nString = useTranslations('Web')
  const toast = useToastContext()
  const { reportEvent } = useVolcAnalytics()
  const { page, pageSize, handlePageNext, handlePageChange } = usePagination(1, 12)
  const { isLoggedIn } = useAuth()
  const { saveCategoryReferrer } = useBreadcrumbs()

  // Refs（引用）
  const initRef = useRef(false)
  const titleRef = useRef<HTMLHeadingElement>(null)

  // 计算值
  const categoryUid = useMemo(() => (isBase64(uid) ? uid : encodeBase64(uid)), [uid])

  // 状态
  const [catalogCategoryNav, setCatalogCategoryNav] = useState<TCategoryNav>()
  const [hasCategories, setHasCategories] = useState(true)
  const [showLeftMenu, setShowLeftMenu] = useState(true)
  const [sort, setSort] = useState<{ [key: string]: 'ASC' | 'DESC' }>({ position: 'ASC' })
  const [banner, setBanner] = useState<BannerData>(null)
  const [products, setProducts] = useState<HomeProduct[]>([])
  const [total, setTotal] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const [loading, setLoading] = useState<LoadingState>({ isLoading: true, isFetching: true })
  const [onlyMyCar, setOnlyMyCar] = useState(false)
  const [isDigital, setIsDigital] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  // 选中的分类UID状态
  const [selectedCategoryUid, setSelectedCategoryUid] = useState(categoryUid)

  // 初始分类数据
  const [initialCategoryData, setInitialCategoryData] = useState<TCategoryItem | null>(
    categoryData || null,
  )

  const {
    currentData: categoriesAllData,
    isLoading,
    isFetching,
    error,
  } = useGetCategoryProductsQuery(
    {
      filters: {
        category_uid: {
          eq: selectedCategoryUid,
        },
      },
      pageSize,
      currentPage: page,
      sort,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
      productFilter: {
        user_device: {
          eq: onlyMyCar ? '1' : '0',
        },
      },
    },
    {
      skip: !selectedCategoryUid,
    },
  )

  const currentCategory = useMemo(() => {
    return categoriesAllData?.categories?.items?.[0]
  }, [categoriesAllData])

  // 分类切换处理
  const handleCategorySelect = useCallback(
    (categoryId: string) => {
      setSelectedCategoryUid(categoryId)

      // 重置分页到第一页，这会触发新的数据请求
      handlePageChange(1)
    },
    [handlePageChange],
  )

  // 获取父级分类数据
  const fetchParentCategoryData = useCallback(async (parentUid: string) => {
    try {
      const response = await getCategoriesQuery({
        filters: {
          category_uid: {
            eq: parentUid,
          },
        },
      })

      if (response.data?.categories?.items?.length) {
        return response.data.categories.items[0]!
      }
    } catch (error) {
      console.warn('Failed to fetch parent category data:', error)
    }
    return null
  }, [])

  // 当categoryUid变化时，重置selectedCategoryUid以确保数据一致性
  useEffect(() => {
    setSelectedCategoryUid(categoryUid)
  }, [categoryUid])

  // 处理传入的完整分类数据
  useEffect(() => {
    if (categoryData && !initialCategoryData) {
      setInitialCategoryData(categoryData)
    }
  }, [categoryData, initialCategoryData])

  // 保存初始分类数据并设置页面结构
  useEffect(() => {
    if (currentCategory && !initialCategoryData) {
      setInitialCategoryData(currentCategory)

      // 设置页面结构相关的状态
      setBanner({
        title: currentCategory.category_banner_title,
        subtitle: currentCategory.category_banner_subtitle,
        image: currentCategory.category_pc_banner,
        description: currentCategory.category_banner_description,
        buttonText: currentCategory.category_banner_button_text,
        url: currentCategory.category_banner_url,
        widthRatio: currentCategory.category_pc_banner_width || 1,
        heightRatio: currentCategory.category_pc_banner_height || 1,
      })

      setIsDigital(Boolean(currentCategory.is_digital))
      setShowLeftMenu(!Boolean(currentCategory.close_left_menu))
    }
  }, [currentCategory, initialCategoryData])

  // 当分类数据变化时进行处理（仅更新产品数据）
  useEffect(() => {
    if (!currentCategory) return

    const newProducts = currentCategory.products?.items || []
    const pageInfo = currentCategory.products?.page_info || {}

    setTotal(currentCategory.products?.total_count || 0)

    // 分类切换时直接替换产品列表，分页加载更多时才合并
    if (page === 1) {
      // 第一页或分类切换时，直接替换产品列表
      setProducts(newProducts as HomeProduct[])
    } else {
      // 分页加载更多时，合并产品列表
      setProducts((prevProducts) => updateProductList(prevProducts, newProducts as HomeProduct[]))
    }

    setHasMore((pageInfo.total_pages || 0) > (pageInfo.current_page || 0))

    // 保存分类referrer信息，用于产品详情页的面包屑导航
    saveCategoryReferrer(currentCategory.uid).catch((error) => {
      console.warn('Failed to save category referrer:', error)
    })

    // 如果是加载更多完成，重置加载更多状态
    if (isLoadingMore && page > 1) {
      setIsLoadingMore(false)
    }

    initRef.current = true
  }, [
    currentCategory,
    hasCategories,
    saveCategoryReferrer,
    isLoadingMore,
    page,
    selectedCategoryUid,
  ])

  // 递归转换分类数据
  const getCategoriesData = useCallback(
    (category: TCategoryItem, parent?: TCategoryNav, includeSiblings = false): TCategoryNav[] => {
      if (!category?.children) return []

      // 如果需要包含同级分类，且存在父级分类，则返回父级分类的所有子分类
      if (includeSiblings && parent) {
        return parent.children || []
      }

      return category.children
        .filter((item) => item?.include_in_menu === 1)
        .map((item) => {
          const itemData: TCategoryNav = {
            id: item?.uid || '',
            name: item?.name || '',
            url: `/${item?.url_path}${item?.url_suffix || ''}`,
            parent,
          }

          if (item?.children?.length) {
            itemData.children = getCategoriesData(item, itemData)
          }

          return itemData
        })
    },
    [],
  )

  // 初始化分类导航
  useEffect(() => {
    const categoryToUse = initialCategoryData || currentCategory
    if (!categoryToUse || catalogCategoryNav?.id) return

    let categoriesData: TCategoryNav[] = []
    const navCategory = categoryToUse

    // 优先根据 breadcrumbs 中是否存在 category_level === 3 来决定左侧导航展示
    const level3Breadcrumb = categoryToUse?.breadcrumbs?.find((b) => b?.category_level === 3)

    if (level3Breadcrumb?.category_uid) {
      // 如果存在 level 3 的面包屑：基于该 level 3 分类构造左侧导航
      // 为了能显示到 level 5，左侧导航的顶层使用 level 4 分类，子层为 level 5 分类
      const targetUid = level3Breadcrumb.category_uid

      const fetchAndSetLevel3Nav = async () => {
        const targetCategory = await fetchParentCategoryData(targetUid!)
        if (targetCategory) {
          // 构建“level 3 导航”时不再根据 include_in_menu 过滤，确保展示完整的同级三级分类
          const rawChildren = (targetCategory.children ?? []) as NonNullable<
            TCategoryItem['children']
          >

          // 以当前 level 3 分类的直接子分类（level 4）作为左侧导航顶层，其 children 为对应的 level 5
          const children: TCategoryNav[] = rawChildren.map((lvl4) => {
            const node: TCategoryNav = {
              id: lvl4?.uid || '',
              name: lvl4?.name || '',
              url: `/${lvl4?.url_path}${lvl4?.url_suffix || ''}`,
            }
            if (lvl4?.children?.length) {
              node.children = lvl4.children.map((lvl5) => ({
                id: lvl5?.uid || '',
                name: lvl5?.name || '',
                url: `/${lvl5?.url_path}${lvl5?.url_suffix || ''}`,
              }))
            }
            return node
          })

          setHasCategories(children.length > 0)
          setCatalogCategoryNav({
            id: targetCategory.uid || '',
            name: targetCategory.name || '',
            url: `/${targetCategory.url_path}${targetCategory.url_suffix || ''}`,
            children,
          })
        } else {
          setHasCategories(false)
        }
      }

      fetchAndSetLevel3Nav()
      return // 异步处理，直接返回
    }

    // 如果没有 level 3，则按原逻辑处理：有子分类则展示子分类；否则尝试根据父级显示同级分类
    if (categoryToUse?.children && categoryToUse.children.length > 0) {
      // 有子分类，显示子分类
      categoriesData = getCategoriesData(categoryToUse)
      setHasCategories(categoriesData.length > 0)
    } else if (categoryToUse?.breadcrumbs && categoryToUse.breadcrumbs.length > 0) {
      // 没有子分类但有面包屑信息，说明有父级分类，尝试显示同级分类
      // 找到 category_level 值最大的面包屑元素作为最近父分类
      const parentBreadcrumb = categoryToUse.breadcrumbs.reduce((prev, current) => {
        if (!prev || !current) return prev || current
        return (prev.category_level || 0) > (current.category_level || 0) ? prev : current
      }, categoryToUse.breadcrumbs[0]!)
      if (parentBreadcrumb && parentBreadcrumb.category_uid) {
        // 异步获取父级分类数据
        const fetchAndSetParentCategory = async () => {
          const parentCategory = await fetchParentCategoryData(parentBreadcrumb.category_uid!)

          console.log('Parent Category Data:', parentCategory)

          if (parentCategory?.children) {
            // 不使用 getCategoriesData，直接基于接口数据构建两级导航（不按 include_in_menu 过滤）
            const rawChildren = (parentCategory.children ?? []) as NonNullable<
              TCategoryItem['children']
            >
            const siblingCategories: TCategoryNav[] = rawChildren.map((lvl2) => {
              const node: TCategoryNav = {
                id: lvl2?.uid || '',
                name: lvl2?.name || '',
                url: `/${lvl2?.url_path}${lvl2?.url_suffix || ''}`,
              }
              if (lvl2?.children?.length) {
                node.children = lvl2.children.map((lvl3) => ({
                  id: lvl3?.uid || '',
                  name: lvl3?.name || '',
                  url: `/${lvl3?.url_path}${lvl3?.url_suffix || ''}`,
                }))
              }
              return node
            })

            setHasCategories(siblingCategories.length > 0)

            // 更新 catalogCategoryNav
            setCatalogCategoryNav({
              id: parentCategory.uid || '',
              name: parentCategory.name || '',
              url: `/${parentCategory.url_path}${parentCategory.url_suffix || ''}`,
              children: siblingCategories,
            })
          } else {
            setHasCategories(false)
            setCatalogCategoryNav({
              id: navCategory.uid || '',
              name: navCategory.name || '',
              url: `/${navCategory.url_path}${navCategory.url_suffix || ''}`,
              children: [],
            })
          }
        }

        fetchAndSetParentCategory()
        return // 异步处理，直接返回
      } else {
        setHasCategories(false)
      }
    } else {
      setHasCategories(false)
    }

    setCatalogCategoryNav({
      id: navCategory.uid || '',
      name: navCategory.name || '',
      url: `/${navCategory.url_path}${navCategory.url_suffix || ''}`,
      children: categoriesData,
    })
  }, [
    initialCategoryData,
    currentCategory,
    catalogCategoryNav?.id,
    getCategoriesData,
    fetchParentCategoryData,
  ])

  // 根据路径名查找当前活动分类
  const activeCategory = useMemo((): TCategoryNav | undefined => {
    if (!catalogCategoryNav) return undefined

    // 检查当前URL是否匹配根分类
    if (pathname === catalogCategoryNav.url) {
      return catalogCategoryNav
    }

    // 在子分类中搜索
    if (hasCategories && catalogCategoryNav.children) {
      // 检查第二级
      for (const child of catalogCategoryNav.children) {
        if (pathname === child.url) {
          return { ...child, parent: catalogCategoryNav }
        }

        // 检查第三级
        if (child.children) {
          const subChild = child.children.find((sub) => pathname === sub.url)
          if (subChild) {
            return {
              ...subChild,
              parent: { ...child, parent: catalogCategoryNav },
            }
          }
        }
      }
    }
    return catalogCategoryNav
  }, [catalogCategoryNav, pathname, hasCategories])

  // 当 activeCategory 更新时，重新处理没有子分类的情况
  useEffect(() => {
    if (!currentCategory || !catalogCategoryNav?.id) return

    // 如果当前分类没有子分类，但 activeCategory 有父级分类，则显示同级分类
    if (!currentCategory?.children && activeCategory?.parent?.children) {
      const siblingCategories = activeCategory.parent.children
      setHasCategories(siblingCategories.length > 0)

      // 更新 catalogCategoryNav 的 children
      setCatalogCategoryNav((prev) => {
        if (!prev) return prev
        return {
          ...prev,
          children: siblingCategories,
        }
      })
    }
  }, [activeCategory, currentCategory, catalogCategoryNav?.id])

  // 处理错误
  useEffect(() => {
    if (error) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
    }
  }, [error, toast])

  const skeletonShowLeftMenu = useMemo(() => {
    if (currentCategory) {
      // 如果分类数据已加载，根据实际条件判断
      const shouldShowLeftMenu = !Boolean(currentCategory.close_left_menu)

      // 检查是否有可显示的分类（子分类或同级分类）
      let hasDisplayableCategories = false

      if (currentCategory?.children) {
        // 有子分类，显示子分类
        const categoriesData = getCategoriesData(currentCategory)
        hasDisplayableCategories = categoriesData.length > 0
      } else {
        // 没有子分类，检查是否有同级分类
        const active = activeCategory
        if (active?.parent?.children) {
          hasDisplayableCategories = active.parent.children.length > 0
        }
      }

      const result = hasDisplayableCategories && shouldShowLeftMenu
      return result
    }
    return false // 默认不显示左侧菜单，避免布局跳跃
  }, [currentCategory, getCategoriesData, activeCategory])

  // 使用防抖更新加载状态
  useEffect(() => {
    setLoading({ isLoading, isFetching })

    const timer = setTimeout(() => {
      setLoading((prev) => ({
        isLoading: isLoading ? prev.isLoading : false,
        isFetching: isFetching ? prev.isFetching : false,
      }))
    }, 100)

    return () => clearTimeout(timer)
  }, [isLoading, isFetching])

  // 跟踪分类曝光
  useEffect(() => {
    if (
      activeCategory?.id &&
      activeCategory?.name &&
      activeCategory?.name !== getI18nString('all')
    ) {
      reportEvent(TRACK_EVENT.shop_small_classfication_exposure, {
        category_id: activeCategory.id,
        category_name: activeCategory.name,
      })
    }
  }, [activeCategory, reportEvent, getI18nString])

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    setIsLoadingMore(true)
    handlePageNext()
  }, [handlePageNext])

  return (
    <>
      <div className="border-b border-[#E1E1E4]">
        <div className="max-container-no-mb">
          {loading.isLoading || !initialCategoryData ? (
            <BreadcrumbSkeleton itemCount={3} />
          ) : (
            <Breadcrumb category={initialCategoryData} currentPath={pathname} />
          )}
        </div>
      </div>
      <div className="max-container-no-mb">
        {loading.isLoading ? (
          <div className="mt-48">
            {skeletonShowLeftMenu ? (
              // 显示左侧导航的骨架屏布局
              <div className="flex flex-row gap-[48px]">
                <div className="w-[25%]">
                  {Array.from({ length: 10 }).map((_, index) => (
                    <Skeleton
                      key={index}
                      style={{ width: '100%', height: 48, marginBottom: 20, borderRadius: 99 }}
                    />
                  ))}
                </div>
                <div className="w-[75%]">
                  <ProductCardSkeleton length={6} perRow={3} />
                </div>
              </div>
            ) : (
              // 不显示左侧导航的骨架屏布局
              <ProductCardSkeleton length={total} perRow={4} />
            )}
          </div>
        ) : (
          <>
            <h1
              className={`mt-[44px] font-miSansDemiBold450 text-h2 ${
                hasCategories && showLeftMenu ? 'mb-[48px]' : 'mb-[24px]'
              }`}>
              {initialCategoryData?.name}
            </h1>
            {hasCategories && showLeftMenu && (
              <h2
                ref={titleRef}
                className="sticky top-[56px] z-20 mb-8 border-b border-[#E1E1E4] bg-white py-base-16 font-miSansDemiBold450 text-[28px] leading-[120%] transition-all duration-300">
                {getI18nString('product_category')}
              </h2>
            )}
            <div className={`${hasCategories && showLeftMenu ? 'flex gap-base-48' : ''}`}>
              {hasCategories && showLeftMenu && (
                <CategoryNav
                  currentCategory={{
                    id: initialCategoryData?.uid || '',
                    name: initialCategoryData?.name || '',
                    url:
                      `/${initialCategoryData?.url_path}${initialCategoryData?.url_suffix}` || '',
                  }}
                  categories={catalogCategoryNav?.children}
                  selectedCategoryId={selectedCategoryUid}
                  onCategorySelect={handleCategorySelect}
                  hideAll={false}
                  firstItem={(function () {
                    // 若左侧结构为：顶层 level 4 且有 children（level 5），则第一个入口替换为 level 3 名称与目标
                    const top = catalogCategoryNav?.children?.[0]
                    const hasLvl5 = Boolean(top?.children && top.children.length)
                    if (hasLvl5) {
                      const lvl3 = initialCategoryData?.breadcrumbs?.find(
                        (b) => b?.category_level === 3,
                      )
                      if (lvl3?.category_uid && lvl3?.category_name) {
                        return { id: lvl3.category_uid, label: lvl3.category_name }
                      }
                    }
                    return undefined
                  })()}
                />
              )}

              <div className="flex-1">
                {loading.isFetching ? (
                  <ProductListSkeleton
                    hasCategories={hasCategories && showLeftMenu}
                    showBanner={!!banner?.image}
                    total={total}
                    isDigital={isDigital}
                    isLoggedIn={isLoggedIn}
                  />
                ) : (
                  <ProductList
                    hasCategories={hasCategories && showLeftMenu}
                    products={products}
                    handlePageNext={handleLoadMore}
                    hasMore={hasMore}
                    total={total}
                    sort={sort}
                    setSort={setSort}
                    currentPage={page}
                    banner={banner}
                    isDigital={isDigital}
                    onlyMyCar={onlyMyCar}
                    setOnlyMyCar={setOnlyMyCar}
                    isLoadingMore={isLoadingMore}
                  />
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default CategoryList
